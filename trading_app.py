#!/usr/bin/env python3
"""
Trading Application - تطبيق التداول
التطبيق الرئيسي للتداول الآلي بالخيارات الثنائية
"""

import asyncio
import sys
import os
import json
import getpass
from datetime import datetime
from typing import Dict, List, Any, Optional

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.trading.auto_trader import AutoTrader
from src.trading.trading_connector import TradingConnector
from src.risk_management.risk_manager import RiskManager

class TradingApp:
    """تطبيق التداول الرئيسي"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.auto_trader = AutoTrader()
        self.connector = TradingConnector()
        self.risk_manager = RiskManager()
        self.is_connected = False
        self.current_account_info = {}
        
        print("🚀 Binary Options Auto Trading System")
        print("="*60)
    
    async def run(self):
        """تشغيل التطبيق الرئيسي"""
        try:
            # الاتصال بالمنصة
            if not await self._connect_to_platform():
                return
            
            # القائمة الرئيسية
            while True:
                choice = await self._show_main_menu()
                
                if choice == '1':
                    await self._switch_account_type()
                elif choice == '2':
                    await self._start_auto_trading()
                elif choice == '3':
                    await self._repeat_last_session()
                elif choice == '4':
                    await self._view_session_history()
                elif choice == '5':
                    await self._view_account_info()
                elif choice == '6':
                    print("👋 Goodbye!")
                    break
                else:
                    print("❌ Invalid choice. Please try again.")
                
                input("\nPress Enter to continue...")
                
        except KeyboardInterrupt:
            print("\n🛑 Application stopped by user")
        except Exception as e:
            print(f"❌ Application error: {e}")
        finally:
            await self._cleanup()
    
    async def _connect_to_platform(self) -> bool:
        """الاتصال بمنصة التداول"""
        try:
            print("\n🔗 Connecting to Quotex Platform")
            print("-" * 40)

            # فحص إذا كان هناك اتصال موجود من main.py
            existing_connection = self._check_existing_connection()

            if existing_connection:
                print("✅ Using existing connection from main.py")
                self.is_connected = True
                self.current_account_info = existing_connection

                print(f"Account Type: {existing_connection.get('account_type', 'PRACTICE')}")
                print(f"Balance: ${existing_connection.get('balance', 10000):.2f}")

                return True

            # إذا لم يكن هناك اتصال موجود، إنشاء اتصال جديد
            print("No existing connection found. Creating new connection...")

            email = input("Email: ").strip()
            password = getpass.getpass("Password: ")

            if not email or not password:
                print("❌ Email and password are required")
                return False

            print("⏳ Connecting...")

            connection_result = await self.auto_trader.connect_to_platform(email, password)

            if connection_result['status'] == 'SUCCESS':
                self.is_connected = True
                self.current_account_info = connection_result

                print("✅ Connected successfully!")
                print(f"Account Type: {connection_result.get('account_type', 'Unknown')}")
                print(f"Balance: ${connection_result.get('balance', 0):.2f}")

                return True
            else:
                print(f"❌ Connection failed: {connection_result.get('error', 'Unknown error')}")
                return False

        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False

    def _check_existing_connection(self) -> Optional[Dict[str, Any]]:
        """فحص الاتصال الموجود من main.py"""
        try:
            # فحص ملف session.json للاتصال الموجود
            session_file = "session.json"
            if os.path.exists(session_file):
                with open(session_file, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)

                # فحص إذا كان الاتصال نشطاً
                if session_data.get('connected', False):
                    return {
                        'status': 'SUCCESS',
                        'account_type': session_data.get('account_type', 'PRACTICE'),
                        'balance': session_data.get('balance', 10000),
                        'connected': True
                    }

            return None

        except Exception as e:
            print(f"⚠️ Could not check existing connection: {e}")
            return None
    
    async def _show_main_menu(self) -> str:
        """عرض القائمة الرئيسية"""
        print("\n" + "="*60)
        print("📊 MAIN MENU")
        print("="*60)
        
        # عرض معلومات الحساب الحالي
        if self.current_account_info:
            account_type = self.current_account_info.get('account_type', 'Unknown')
            balance = self.current_account_info.get('balance', 0)
            print(f"Current Account: {account_type} | Balance: ${balance:.2f}")
            print("-" * 60)
        
        print("1. Switch Account Type (Practice/Real)")
        print("2. Start Auto Trading")
        print("3. Repeat Last Session")
        print("4. View Session History")
        print("5. View Account Info")
        print("6. Exit")
        print("="*60)
        
        return input("Select option (1-6): ").strip()
    
    async def _switch_account_type(self):
        """تبديل نوع الحساب"""
        try:
            print("\n🔄 Switch Account Type")
            print("-" * 30)
            print("1. Practice Account")
            print("2. Real Account")
            
            choice = input("Select account type (1-2): ").strip()
            
            if choice == '1':
                account_type = 'PRACTICE'
            elif choice == '2':
                account_type = 'REAL'
                
                # تأكيد للحساب الحقيقي
                confirm = input("⚠️  Are you sure you want to switch to REAL account? (yes/no): ").strip().lower()
                if confirm != 'yes':
                    print("❌ Cancelled")
                    return
            else:
                print("❌ Invalid choice")
                return
            
            print(f"⏳ Switching to {account_type} account...")
            
            result = await self.connector.switch_account_type(account_type)
            
            if result['status'] == 'SUCCESS':
                self.current_account_info.update(result)
                print(f"✅ Switched to {account_type} account")
                print(f"New Balance: ${result.get('balance', 0):.2f}")
            else:
                print(f"❌ Failed to switch account: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Error switching account: {e}")
    
    async def _start_auto_trading(self):
        """بدء التداول الآلي"""
        try:
            print("\n🤖 Auto Trading Setup")
            print("="*40)
            
            # الحصول على أفضل الأزواج
            print("⏳ Loading available pairs...")
            pairs = await self.connector.get_available_pairs()
            
            if not pairs:
                print("❌ No pairs available")
                return
            
            # عرض أفضل 20 زوج
            print("\n📈 Top 20 Pairs by Payout:")
            print("-" * 40)
            for i, pair in enumerate(pairs[:20], 1):
                print(f"{i:2d}. {pair['symbol']} - {pair['payout_percentage']:.1f}%")
            
            # اختيار الزوج
            try:
                pair_choice = int(input("\nSelect pair (1-20): ").strip())
                if 1 <= pair_choice <= len(pairs):
                    selected_pair = pairs[pair_choice - 1]['symbol']
                else:
                    print("❌ Invalid pair selection")
                    return
            except ValueError:
                print("❌ Invalid input")
                return
            
            # إعدادات التداول
            print(f"\n⚙️ Trading Settings for {selected_pair}")
            print("-" * 40)
            
            # مبلغ الصفقة
            current_balance = self.current_account_info.get('balance', 0)
            recommended_amount = self.risk_manager.get_recommended_trade_amount(current_balance)
            
            print(f"Current Balance: ${current_balance:.2f}")
            print(f"Recommended Amount (2%): ${recommended_amount:.2f}")
            
            amount_input = input(f"Trade Amount (default ${recommended_amount:.2f}): ").strip()
            
            if amount_input:
                try:
                    trade_amount = float(amount_input)
                except ValueError:
                    print("❌ Invalid amount")
                    return
            else:
                trade_amount = recommended_amount
            
            # عدد الصفقات
            try:
                max_trades = int(input("Number of trades in session (default 10): ").strip() or "10")
            except ValueError:
                print("❌ Invalid number")
                return
            
            # تأكيد الإعدادات
            print(f"\n📋 Session Configuration:")
            print(f"Account Type: {self.current_account_info.get('account_type', 'Unknown')}")
            print(f"Balance: ${current_balance:.2f}")
            print(f"Selected Pair: {selected_pair}")
            print(f"Trade Amount: ${trade_amount:.2f}")
            print(f"Max Trades: {max_trades}")
            
            confirm = input("\n✅ Are you sure? (yes/no): ").strip().lower()
            
            if confirm != 'yes':
                print("❌ Cancelled")
                return
            
            # إعداد الجلسة
            print("\n⚙️ Setting up trading session...")
            
            setup_result = await self.auto_trader.setup_trading_session(
                account_type=self.current_account_info.get('account_type', 'PRACTICE'),
                pair=selected_pair,
                trade_amount=trade_amount,
                max_trades=max_trades
            )
            
            if setup_result['status'] != 'SUCCESS':
                print(f"❌ Setup failed: {setup_result.get('error', 'Unknown error')}")
                return
            
            # بدء التداول
            print("🚀 Starting automated trading...")
            print("⏳ Waiting for trading signals...")
            print("Press Ctrl+C to stop trading")
            
            trading_result = await self.auto_trader.start_auto_trading()
            
            if trading_result['status'] == 'SUCCESS':
                print("\n🏁 Trading session completed!")
                
                # عرض النتائج
                final_stats = trading_result.get('final_stats', {})
                self._display_session_results(final_stats)
            else:
                print(f"❌ Trading failed: {trading_result.get('error', 'Unknown error')}")
                
        except KeyboardInterrupt:
            print("\n🛑 Trading stopped by user")
            await self.auto_trader.stop_trading()
        except Exception as e:
            print(f"❌ Error in auto trading: {e}")
    
    async def _repeat_last_session(self):
        """تكرار الجلسة السابقة"""
        try:
            print("\n🔄 Repeat Last Session")
            print("-" * 30)
            
            # البحث عن آخر جلسة
            sessions_dir = "data/sessions"
            if not os.path.exists(sessions_dir):
                print("❌ No previous sessions found")
                return
            
            session_files = [f for f in os.listdir(sessions_dir) if f.startswith('session_') and f.endswith('.json')]
            
            if not session_files:
                print("❌ No previous sessions found")
                return
            
            # أحدث جلسة
            latest_session = max(session_files)
            session_path = os.path.join(sessions_dir, latest_session)
            
            with open(session_path, 'r', encoding='utf-8') as f:
                last_session = json.load(f)
            
            print(f"📋 Last Session Details:")
            print(f"Date: {last_session.get('start_time', 'Unknown')}")
            print(f"Account: {last_session.get('account_type', 'Unknown')}")
            print(f"Trades: {last_session.get('trades_count', 0)}")
            print(f"Wins: {last_session.get('wins', 0)}")
            print(f"Losses: {last_session.get('losses', 0)}")
            
            confirm = input("\n🔄 Repeat this session? (yes/no): ").strip().lower()
            
            if confirm == 'yes':
                print("🚀 Starting repeated session...")
                # هنا يمكن تنفيذ تكرار الجلسة
                print("⚠️  Feature coming soon!")
            else:
                print("❌ Cancelled")
                
        except Exception as e:
            print(f"❌ Error repeating session: {e}")
    
    async def _view_session_history(self):
        """عرض تاريخ الجلسات"""
        try:
            print("\n📊 Session History")
            print("="*50)
            
            sessions_dir = "data/sessions"
            if not os.path.exists(sessions_dir):
                print("❌ No sessions found")
                return
            
            session_files = [f for f in os.listdir(sessions_dir) if f.startswith('session_') and f.endswith('.json')]
            
            if not session_files:
                print("❌ No sessions found")
                return
            
            # ترتيب الجلسات حسب التاريخ
            session_files.sort(reverse=True)
            
            for i, session_file in enumerate(session_files[:10], 1):  # آخر 10 جلسات
                session_path = os.path.join(sessions_dir, session_file)
                
                try:
                    with open(session_path, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                    
                    start_time = session_data.get('start_time', 'Unknown')
                    account_type = session_data.get('account_type', 'Unknown')
                    trades = session_data.get('trades_count', 0)
                    wins = session_data.get('wins', 0)
                    losses = session_data.get('losses', 0)
                    win_rate = (wins / trades * 100) if trades > 0 else 0
                    
                    print(f"{i:2d}. {start_time[:19]} | {account_type:8} | "
                          f"Trades: {trades:2d} | Wins: {wins:2d} | Win Rate: {win_rate:5.1f}%")
                    
                except Exception as e:
                    print(f"{i:2d}. Error reading {session_file}: {e}")
            
        except Exception as e:
            print(f"❌ Error viewing session history: {e}")
    
    async def _view_account_info(self):
        """عرض معلومات الحساب"""
        try:
            print("\n💰 Account Information")
            print("="*40)
            
            account_info = await self.connector.get_account_info()
            
            if account_info['status'] == 'SUCCESS':
                print(f"Account Type: {account_info.get('account_type', 'Unknown')}")
                print(f"Balance: ${account_info.get('balance', 0):.2f}")
                print(f"Connected: {account_info.get('connected', False)}")
                
                # تحديث المعلومات المحلية
                self.current_account_info.update(account_info)
            else:
                print(f"❌ Could not get account info: {account_info.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Error getting account info: {e}")
    
    def _display_session_results(self, stats: Dict[str, Any]):
        """عرض نتائج الجلسة"""
        print("\n📊 Session Results")
        print("="*40)
        
        total_trades = stats.get('trades_executed', 0)
        wins = stats.get('wins', 0)
        losses = stats.get('losses', 0)
        total_profit = stats.get('total_profit', 0)
        win_rate = (wins / total_trades * 100) if total_trades > 0 else 0
        
        print(f"Total Trades: {total_trades}")
        print(f"Wins: {wins}")
        print(f"Losses: {losses}")
        print(f"Win Rate: {win_rate:.1f}%")
        print(f"Total Profit: ${total_profit:.2f}")
        
        if win_rate >= 80:
            print("🎉 Excellent performance!")
        elif win_rate >= 70:
            print("👍 Good performance!")
        elif win_rate >= 60:
            print("👌 Acceptable performance")
        else:
            print("⚠️  Performance needs improvement")
    
    async def _cleanup(self):
        """تنظيف الموارد"""
        try:
            if self.is_connected:
                await self.connector.disconnect()
                print("🔌 Disconnected from platform")
        except Exception as e:
            print(f"Error during cleanup: {e}")

async def main():
    """الدالة الرئيسية"""
    app = TradingApp()
    await app.run()

if __name__ == "__main__":
    asyncio.run(main())
