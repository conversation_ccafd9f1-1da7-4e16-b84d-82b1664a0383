{"training_type": "quick_training", "timestamp": "2025-08-07T02:53:54.162132", "results": {"training_samples": 148, "test_samples": 37, "feature_count": 25, "model_results": {"random_forest": {"accuracy": 0.4864864864864865, "cv_mean": 0.4519540229885058, "cv_std": 0.09803836328929297, "feature_importance": [0.04870387298960841, 0.04699070180774159, 0.04443646416364859, 0.05516114578098588, 0.053189446834902065, 0.0742725581841334, 0.07470054973491673, 0.07971694424379012, 0.05000790200083159, 0.05587285074226623, 0.04902748785626498, 0.032716049758807736, 0.0, 0.04116750865014435, 0.046030787541707155, 0.04989399898571637, 0.049097048588595794, 0.041585940221395744, 0.0, 0.0, 0.055960507954663966, 0.0, 0.0, 0.0, 0.05146823395987936]}, "xgboost": {"accuracy": 0.4864864864864865, "cv_mean": 0.41287356321839075, "cv_std": 0.11850881296412155, "feature_importance": [0.04114624112844467, 0.07597783952951431, 0.05559740960597992, 0.04509098082780838, 0.07404742389917374, 0.059952665120363235, 0.061077624559402466, 0.06264292448759079, 0.057058174163103104, 0.057872116565704346, 0.049606312066316605, 0.038762547075748444, 0.0, 0.039007265120744705, 0.06182986870408058, 0.04614981263875961, 0.06592981517314911, 0.05397564172744751, 0.0, 0.0, 0.05427532270550728, 0.0, 0.0, 0.0, 0.0]}, "gradient_boosting": {"accuracy": 0.40540540540540543, "cv_mean": 0.4533333333333333, "cv_std": 0.07850700929632794, "feature_importance": [0.030154644578275318, 0.04446618810476813, 0.020222407852555788, 0.06369950010641189, 0.012784791014348195, 0.08802856927043504, 0.09883045444000484, 0.09219402578926399, 0.05705126578166363, 0.035487546427312375, 0.08718404040870335, 0.01756936956294001, 0.0, 0.06745692377582964, 0.06934383688065139, 0.04035031044148321, 0.02739892876830186, 0.04688025147483773, 0.0, 0.0, 0.06300571412462777, 0.0, 0.0, 0.0, 0.03789123119758571]}}, "best_model": "gradient_boosting"}, "status": "completed"}