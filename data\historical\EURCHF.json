{"pair": "EURCHF", "timeframe": 300, "timeframe_minutes": 5, "last_updated": "2025-08-07T02:58:51.000027", "candles_count": 10, "candles": [{"time": 1754521500, "open": 0.9391, "close": 0.93864, "high": 0.9392, "low": 0.93854, "ticks": 98, "datetime": "2025-08-07 02:05:00"}, {"time": 1754521800, "open": 0.93864, "close": 0.93989, "high": 0.94005, "low": 0.93817, "ticks": 701, "datetime": "2025-08-07 02:10:00"}, {"time": 1754522100.0, "open": 0.94006, "close": 0.94005, "high": 0.94006, "low": 0.94005, "ticks": 100, "datetime": "2025-08-07 02:15:00", "last_update": 1754522399.524}, {"time": 1754522400.0, "open": 0.94003, "close": 0.94006, "high": 0.94008, "low": 0.94002, "ticks": 2750, "datetime": "2025-08-07 02:20:00", "last_update": 1754522699.074}, {"time": 1754522700.0, "open": 0.94006, "close": 0.94005, "high": 0.94007, "low": 0.94002, "ticks": 2731, "datetime": "2025-08-07 02:25:00", "last_update": 1754522999.03}, {"time": 1754523000.0, "open": 0.94005, "close": 0.94016, "high": 0.94016, "low": 0.94005, "ticks": 2741, "datetime": "2025-08-07 02:30:00", "last_update": 1754523299.711}, {"time": 1754523300.0, "open": 0.94016, "close": 0.94017, "high": 0.9402, "low": 0.94013, "ticks": 2726, "datetime": "2025-08-07 02:35:00", "last_update": 1754523599.074}, {"time": 1754523600.0, "open": 0.94017, "close": 0.94015, "high": 0.94018, "low": 0.94012, "ticks": 2717, "datetime": "2025-08-07 02:40:00", "last_update": 1754523899.073}, {"time": 1754523900.0, "open": 0.94015, "close": 0.94016, "high": 0.94021, "low": 0.94012, "ticks": 2728, "datetime": "2025-08-07 02:45:00", "last_update": 1754524199.523}, {"time": 1754524200.0, "open": 0.94016, "close": 0.94023, "high": 0.94028, "low": 0.94016, "ticks": 2726, "datetime": "2025-08-07 02:50:00", "last_update": 1754524499.073}], "live_candle": {"time": 1754524500.0, "open": 0.94023, "close": 0.94026, "high": 0.94028, "low": 0.94017, "ticks": 2118, "datetime": "2025-08-07 02:55:00", "last_update": 1754524617.007}}